const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const puppeteer = require('puppeteer');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const RuntimeErrorHandler = require('../build-time-repair/RuntimeErrorHandler');
const BrowserDetector = require('./BrowserDetector');
const BuildFixAgent = require('../ai/BuildFixAgent');
const AutoLoginManager = require('./AutoLoginManager');

const execAsync = promisify(exec);

/**
 * PageValidator - 页面运行时验证器
 *
 * 功能：
 * 1. 启动开发服务器
 * 2. 使用 Puppeteer 访问每个路由页面
 * 3. 捕获页面错误和控制台输出
 * 4. 集成 RuntimeErrorHandler 进行错误处理
 * 5. 生成验证报告
 */
class PageValidator {
  constructor(projectPath, routes, options = {}) {
    this.projectPath = projectPath;
    this.allRoutes = routes || [];
    this.routeParser = options.routeParser || null; // 路由解析器实例

    // 过滤路由（如果指定了特定路由）
    if (options.specificRoutes && options.specificRoutes.length > 0) {
      this.routes = this.allRoutes.filter(route =>
        options.specificRoutes.includes(route.path)
      );
      console.log(chalk.yellow(`🎯 只验证指定的 ${this.routes.length} 个路由: ${options.specificRoutes.join(', ')}`));
    } else {
      this.routes = this.allRoutes;
    }
    this.options = {
      port: 3000,
      timeout: 30000,
      headless: 'new', // 使用新的 headless 模式
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      autoFix: false,
      maxFixAttempts: 3, // 最大修复尝试次数
      revalidateAfterFix: true, // 修复后重新验证页面
      dryRun: false, // 预览模式，不实际修改文件
      waitForServer: 60000,  // 增加到60秒
      pageTimeout: 10000,   // 增加到10秒
      executablePath: null, // 浏览器可执行文件路径
      routerMode: 'hash', // Vue Router模式: 'hash' 或 'history'
      loginCredentials: {   // 登录凭据
        username: 'admin',
        password: '111111'
      },
      skipLogin: false,     // 是否跳过自动登录
      ...options
    };

    this.baseUrl = this.options.baseUrl || `http://localhost:${this.options.port}`;
    this.devServer = null;
    this.browser = null;
    this.validationResults = [];
    this.errors = [];
    this.isLoggedIn = false; // 登录状态标记

    // 浏览器检测器
    this.browserDetector = new BrowserDetector({
      verbose: this.options.verbose,
      preferredBrowsers: ['chrome', 'chromium', 'edge']
    });

    // 集成运行时错误处理器
    if (this.options.autoFix) {
      this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
        port: this.options.port,
        autoFix: true,
        verbose: this.options.verbose
      });
    }

    // 集成 BuildFixAgent 用于页面错误修复
    if (this.options.autoFix) {
      this.buildFixAgent = new BuildFixAgent(projectPath, {
        maxAttempts: this.options.maxFixAttempts || 3,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun || false
      });
    }

    // 初始化自动登录管理器
    this.autoLoginManager = new AutoLoginManager({
      username: this.options.loginCredentials?.username || this.options.username || 'admin',
      password: this.options.loginCredentials?.password || this.options.password || '111111',
      verbose: this.options.verbose,
      aiEnabled: !this.options.skipLogin,
      configPath: path.join(projectPath, '.login-config.json')
    });
  }

  /**
   * 验证所有页面
   */
  async validateAllPages() {
    console.log(chalk.blue(`🔍 开始验证 ${this.routes.length} 个页面...`));

    try {
      // 1. 启动开发服务器
      await this.startDevServer();

      // 2. 启动浏览器
      await this.startBrowser();

      // 3. 验证每个页面
      for (let i = 0; i < this.routes.length; i++) {
        const route = this.routes[i];
        console.log(chalk.gray(`   [${i + 1}/${this.routes.length}] 验证页面: ${route.path}`));

        const result = await this.validateSinglePage(route);
        this.validationResults.push(result);

        // 短暂延迟避免过快访问 - 减少延迟时间
        await this.sleep(200);
      }

      // 4. 生成报告
      const report = this.generateReport();

      console.log(chalk.green(`✅ 页面验证完成`));
      this.printSummary();

      return {
        success: true,
        results: this.validationResults,
        report: report,
        errors: this.errors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 页面验证失败: ${error.message}`));
      return {
        success: false,
        results: this.validationResults,
        report: null,
        errors: [...this.errors, error.message]
      };
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    if (this.options.baseUrl) {
      console.log(chalk.gray(`   使用外部服务器: ${this.baseUrl}`));
      return;
    }

    console.log(chalk.gray(`   启动开发服务器...`));

    return new Promise((resolve, reject) => {
      // 解析开发命令
      const [command, ...args] = this.options.devCommand.split(' ');

      this.devServer = spawn(command, args, {
        cwd: this.projectPath,
        stdio: 'pipe', // 总是使用 pipe 以便监听输出
        env: {
          ...process.env,
          PORT: this.options.port.toString()
        }
      });

      let serverReady = false;
      let output = '';
      let detectedPort = null;

      // 监听输出判断服务器是否启动
      if (this.devServer.stdout) {
        this.devServer.stdout.on('data', (data) => {
          const text = data.toString();
          output += text;

          if (this.options.verbose) {
            console.log(text);
          }

          // 添加更多调试信息
          // if (this.options.verbose && !serverReady) {
          //   console.log(chalk.cyan(`   🔍 检查文本: "${text.trim()}"`));
          //   console.log(chalk.cyan(`   🔍 包含 'Local:': ${text.includes('Local:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'localhost:': ${text.includes('localhost:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'App running at': ${text.includes('App running at')}`));
          // }

          // 检查服务器启动标志并提取端口
          if (!serverReady && (
            text.includes('Local:') ||
            text.includes('localhost:') ||
            text.includes('App running at') ||
            text.includes('Network:')
          )) {
            if (this.options.verbose) {
              console.log(chalk.blue(`   🔍 检测到服务器启动信息: ${text.trim()}`));
            }

            // 尝试提取端口号
            const portMatch = text.match(/localhost:(\d+)/);
            if (portMatch) {
              detectedPort = parseInt(portMatch[1]);
              if (this.options.verbose) {
                console.log(chalk.gray(`   检测到服务器端口: ${detectedPort}`));
              }

              // 验证服务器是否真的可以访问
              this.verifyServerWithRetry(detectedPort, resolve, reject);
            }
          }
        });
      }

      if (this.devServer.stderr) {
        this.devServer.stderr.on('data', (data) => {
          const text = data.toString();
          if (this.options.verbose) {
            console.error(chalk.red(text));
          }
        });
      }

      this.devServer.on('error', (error) => {
        reject(new Error(`启动开发服务器失败: ${error.message}`));
      });

      this.devServer.on('exit', (code) => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`开发服务器异常退出，代码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error(`开发服务器启动超时 (${this.options.waitForServer}ms)`));
        }
      }, this.options.waitForServer);
    });
  }

  /**
   * 带重试的服务器验证
   */
  async verifyServerWithRetry(port, resolve, reject, attempt = 1, maxAttempts = 5) {
    const delay = attempt * 2000; // 递增延迟：2s, 4s, 6s, 8s, 10s

    setTimeout(async () => {
      try {
        const isReady = await this.verifyServerReady(port);

        if (isReady) {
          this.options.port = port; // 更新实际端口
          this.baseUrl = `http://localhost:${port}`;
          if (this.options.verbose) {
            console.log(chalk.green(`   ✅ 服务器验证成功 (端口: ${port}, 尝试: ${attempt}/${maxAttempts})`));
          }
          resolve();
        } else if (attempt < maxAttempts) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`   ⚠️  端口 ${port} 验证失败，${delay/1000}秒后重试 (${attempt}/${maxAttempts})...`));
          }
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        } else {
          if (this.options.verbose) {
            console.log(chalk.red(`   ❌ 服务器验证失败，已达到最大重试次数 (${maxAttempts})`));
          }
          // 最后一次尝试失败，但不要 reject，让超时处理
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`   ⚠️  服务器验证出错 (尝试 ${attempt}/${maxAttempts}): ${error.message}`));
        }
        if (attempt < maxAttempts) {
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        }
      }
    }, delay);
  }

  /**
   * 验证服务器是否真的可以访问
   */
  async verifyServerReady(port) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://localhost:${port}`, {
        timeout: 5000,
        validateStatus: () => true // 接受任何状态码
      });

      return response.status < 500; // 只要不是服务器错误就认为可用
    } catch (error) {
      return false;
    }
  }

  /**
   * 确保浏览器可用
   */
  async ensureBrowser() {
    try {
      const selectedBrowser = await this.browserDetector.ensureBrowser();

      // 如果选择的是系统浏览器，设置可执行文件路径
      if (selectedBrowser.type !== 'puppeteer') {
        this.options.executablePath = selectedBrowser.executablePath;
      }

      return selectedBrowser;
    } catch (error) {
      throw new Error(`浏览器检测失败: ${error.message}`);
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.gray(`   启动浏览器...`));

    // 确保浏览器可用
    await this.ensureBrowser();

    const launchOptions = {
      headless: this.options.headless === 'new' ? 'new' : this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    };

    // 如果检测到系统浏览器，使用它
    if (this.options.executablePath) {
      launchOptions.executablePath = this.options.executablePath;
    }

    try {
      this.browser = await puppeteer.launch(launchOptions);
      console.log(chalk.green('✅ 浏览器启动成功'));
    } catch (error) {
      // 如果启动失败，尝试使用新的 headless 模式
      if (!launchOptions.headless || launchOptions.headless === true) {
        console.log(chalk.yellow('⚠️  尝试使用新的 headless 模式...'));
        launchOptions.headless = 'new';
        this.browser = await puppeteer.launch(launchOptions);
        console.log(chalk.green('✅ 浏览器启动成功 (新 headless 模式)'));
      } else {
        throw error;
      }
    }
  }

  /**
   * 构建页面URL
   */
  buildPageUrl(route) {
    if (this.options.routerMode === 'hash') {
      // Hash模式: http://localhost:9527/#/example/list
      return `${this.baseUrl}/#${route.path}`;
    } else {
      // History模式: http://localhost:9527/example/list
      return `${this.baseUrl}${route.path}`;
    }
  }

  /**
   * 验证单个页面
   */
  async validateSinglePage(route) {
    const url = this.buildPageUrl(route);
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      loadTime: 0,
      timestamp: new Date().toISOString(),
      needsLogin: false,
      loginAttempted: false
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 设置页面事件监听
      this.setupPageListeners(page, result);

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.pageTimeout);

      // 尝试自动登录（如果需要且还没有登录）
      if (!this.isLoggedIn && !this.options.skipLogin) {
        const loginSuccess = await this.autoLoginManager.attemptLogin(page);
        if (loginSuccess) {
          this.isLoggedIn = true;
          result.loginAttempted = true;
        }
      }

      // 访问页面 - 使用更宽松的等待条件
      const response = await page.goto(url, {
        waitUntil: 'domcontentloaded', // 改为更快的等待条件
        timeout: this.options.pageTimeout
      });

      result.loadTime = Date.now() - startTime;

      // 检查响应状态
      if (!response.ok()) {
        result.errors.push(`HTTP ${response.status()}: ${response.statusText()}`);
      }

      // 检查是否被重定向到登录页面
      const finalUrl = page.url();
      if (finalUrl.includes('/login') && !url.includes('/login')) {
        result.needsLogin = true;

        if (this.options.verbose) {
          console.log(chalk.yellow(`    🔐 页面被重定向到登录页面: ${finalUrl}`));
        }

        // 如果还没有尝试过登录，现在尝试
        if (!result.loginAttempted && !this.isLoggedIn) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`    🔑 尝试登录后重新访问页面...`));
          }

          result.loginAttempted = true;
          const loginSuccess = await this.autoLoginManager.attemptLogin(page);

          if (loginSuccess) {
            this.isLoggedIn = true;

            if (this.options.verbose) {
              console.log(chalk.green(`    🔄 登录成功，重新访问目标页面...`));
            }

            // 登录成功后重新访问目标页面
            await page.goto(url, {
              waitUntil: 'domcontentloaded',
              timeout: this.options.pageTimeout
            });

            const retryUrl = page.url();
            if (!retryUrl.includes('/login')) {
              if (this.options.verbose) {
                console.log(chalk.green(`    ✅ 登录后成功访问页面: ${retryUrl}`));
              }
            } else {
              result.errors.push('登录后仍被重定向到登录页面，可能是权限不足');
            }
          } else {
            result.errors.push('页面需要登录权限，自动登录失败');
          }
        } else if (this.isLoggedIn) {
          // 已经登录但仍被重定向，可能是权限不足
          result.errors.push('页面需要特定权限，当前用户权限不足');
        } else {
          result.errors.push('页面需要登录权限，被重定向到登录页面');
        }
      }

      // 等待页面渲染和Vue应用初始化 - 减少等待时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查页面是否包含 Vue 应用
      const hasVueApp = await page.evaluate(() => {
        return !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
      });

      if (!hasVueApp) {
        result.warnings.push('页面可能未正确加载 Vue 应用');
      }

      // 截图功能 - 为每个页面截图
      try {
        const screenshotPath = await this.takeScreenshot(page, route);
        result.screenshotPath = screenshotPath;
        if (this.options.verbose) {
          console.log(chalk.gray(`    📸 页面截图已保存: ${screenshotPath}`));
        }
      } catch (screenshotError) {
        result.warnings.push(`截图失败: ${screenshotError.message}`);
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  截图失败: ${screenshotError.message}`));
        }
      }

      // 主动检测错误弹框和错误信息
      const errorDetection = await page.evaluate(() => {
        const errors = [];

        // 检查是否有包含"Uncaught runtime errors"的元素
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
          const text = element.textContent || element.innerText || '';

          // 检查错误弹框
          if (text.includes('Uncaught runtime errors') ||
              text.includes('Cannot read properties of null') ||
              text.includes('TypeError:') ||
              text.includes('ReferenceError:') ||
              text.includes('Error:')) {
            errors.push({
              type: 'error-dialog',
              message: text.substring(0, 500),
              tagName: element.tagName,
              className: element.className,
              id: element.id
            });
          }
        }

        // 检查页面标题是否包含错误
        if (document.title.includes('Error') || document.title.includes('错误')) {
          errors.push({
            type: 'page-title-error',
            message: `页面标题包含错误: ${document.title}`
          });
        }

        // 检查控制台是否有错误（通过检查是否有错误样式的元素）
        const errorStyleElements = document.querySelectorAll([
          '.error',
          '.Error',
          '[class*="error"]',
          '[class*="Error"]',
          '.runtime-error',
          '.js-error'
        ].join(','));

        for (const element of errorStyleElements) {
          const text = element.textContent || element.innerText || '';
          if (text.length > 10) {
            errors.push({
              type: 'error-styled-element',
              message: text.substring(0, 300),
              tagName: element.tagName,
              className: element.className
            });
          }
        }

        return errors;
      });

      // 将检测到的错误添加到结果中
      for (const error of errorDetection) {
        result.errors.push({
          type: 'dom-error-detection',
          message: error.message,
          details: error
        });
      }

      // 多次检查错误，因为有些错误可能延迟出现
      let errorData = { pageErrors: [], vueErrors: [], consoleErrors: [] };

      // 第一次检查
      const firstCheck = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 合并第一次检查的结果（安全检查）
      if (firstCheck && firstCheck.pageErrors) {
        errorData.pageErrors.push(...firstCheck.pageErrors);
      }
      if (firstCheck && firstCheck.vueErrors) {
        errorData.vueErrors.push(...firstCheck.vueErrors);
      }
      if (firstCheck && firstCheck.consoleErrors) {
        errorData.consoleErrors.push(...firstCheck.consoleErrors);
      }

      // 等待异步错误出现 - 减少等待时间
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 第二次检查
      const secondCheck = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 合并新的错误（避免重复）
      const existingErrorMessages = new Set(errorData.pageErrors.map(e => e.message));
      const existingVueMessages = new Set(errorData.vueErrors.map(e => e.message));
      const existingConsoleMessages = new Set(errorData.consoleErrors.map(e => e.message));

      // 安全检查第二次检查的结果
      if (secondCheck && secondCheck.pageErrors) {
        for (const error of secondCheck.pageErrors) {
          if (!existingErrorMessages.has(error.message)) {
            errorData.pageErrors.push(error);
          }
        }
      }

      if (secondCheck && secondCheck.vueErrors) {
        for (const error of secondCheck.vueErrors) {
          if (!existingVueMessages.has(error.message)) {
            errorData.vueErrors.push(error);
          }
        }
      }

      if (secondCheck && secondCheck.consoleErrors) {
        for (const error of secondCheck.consoleErrors) {
          if (!existingConsoleMessages.has(error.message)) {
            errorData.consoleErrors.push(error);
          }
        }
      }

      // 处理页面错误
      if (errorData.pageErrors.length > 0) {
        for (const error of errorData.pageErrors) {
          result.errors.push({
            type: error.type || 'javascript-error',
            message: error.message || 'Unknown error',
            details: error
          });
        }
      }

      // 处理 Vue 错误
      if (errorData.vueErrors.length > 0) {
        for (const error of errorData.vueErrors) {
          if (error.type && error.type.includes('warning')) {
            result.warnings.push(`Vue Warning: ${error.message}`);
          } else {
            result.errors.push({
              type: 'vue-error',
              message: error.message || 'Unknown Vue error',
              details: error
            });
          }
        }
      }

      // 处理控制台错误
      if (errorData.consoleErrors.length > 0) {
        for (const error of errorData.consoleErrors) {
          if (error.level === 'error') {
            result.errors.push({
              type: 'console-error',
              message: error.message || 'Unknown console error',
              details: error
            });
          } else if (error.level === 'warn') {
            result.warnings.push(`Console Warning: ${error.message}`);
          }
        }
      }

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      } else if (this.options.autoFix && this.buildFixAgent) {
        // 如果启用了自动修复且有错误，尝试使用 BuildFixAgent 修复
        console.log(chalk.yellow(`  🔧 检测到 ${result.errors.length} 个错误，尝试自动修复...`));
        const fixResult = await this.attemptPageErrorFix(route, result.errors);

        if (fixResult.success) {
          console.log(chalk.green(`  ✅ 页面错误修复成功，修复了 ${fixResult.filesModified} 个文件`));
          result.fixAttempted = true;
          result.fixResult = fixResult;

          // 可选：重新验证页面以确认修复效果
          if (this.options.revalidateAfterFix) {
            console.log(chalk.gray(`  🔄 重新验证页面...`));
            await page.reload({ waitUntil: 'networkidle0' });
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 重新检查错误
            const revalidationErrors = await this.checkPageErrors(page);
            if (revalidationErrors.length < result.errors.length) {
              console.log(chalk.green(`  ✅ 修复后错误减少: ${result.errors.length} → ${revalidationErrors.length}`));
              result.errorsAfterFix = revalidationErrors;
              if (revalidationErrors.length === 0) {
                result.success = true;
              }
            }
          }
        } else {
          console.log(chalk.yellow(`  ⚠️  页面错误修复失败: ${fixResult.error}`));
          result.fixAttempted = true;
          result.fixResult = fixResult;
        }
      }

      await page.close();

    } catch (error) {
      result.errors.push(`页面访问失败: ${error.message}`);

      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${route.path}: ${error.message}`));
      }
    }

    return result;
  }

  /**
   * 为页面截图
   */
  async takeScreenshot(page, route) {
    // 确保截图目录存在
    const screenshotDir = path.join(this.projectPath, 'validation-reports', 'screenshots');
    await fs.ensureDir(screenshotDir);

    // 生成截图文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const routeName = route.path.replace(/[\/\?#]/g, '_').replace(/^_/, '') || 'root';
    const filename = `${routeName}_${timestamp}.png`;
    const screenshotPath = path.join(screenshotDir, filename);

    // 设置视口大小
    await page.setViewport({ width: 1280, height: 800 });

    // 截图
    await page.screenshot({
      path: screenshotPath,
      fullPage: true,
      type: 'png'
    });

    return screenshotPath;
  }

  /**
   * 设置页面事件监听器
   */
  setupPageListeners(page, result) {
    // 监听控制台消息
    page.on('console', (msg) => {
      const message = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 将错误和警告添加到结果中
      if (msg.type() === 'error') {
        result.errors.push(`Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warning') {
        result.warnings.push(`Console Warning: ${msg.text()}`);
      }
    });

    // 监听页面错误
    page.on('pageerror', (error) => {
      result.errors.push(`Page Error: ${error.message}`);
    });

    // 监听请求失败
    page.on('requestfailed', (request) => {
      const networkError = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error'
      };

      result.networkErrors.push(networkError);

      // 只有关键资源失败才算错误
      if (request.resourceType() === 'document' || request.resourceType() === 'script') {
        result.errors.push(`Network Error: ${request.url()} - ${networkError.failure}`);
      }
    });

    // 注入增强的错误收集脚本
    page.evaluateOnNewDocument(() => {
      window.__pageErrors = [];
      window.__vueErrors = [];
      window.__consoleErrors = [];

      // 捕获全局错误
      window.addEventListener('error', (event) => {
        const errorInfo = {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          timestamp: new Date().toISOString(),
          type: 'javascript-error'
        };
        window.__pageErrors.push(errorInfo);
        console.error('Captured global error:', errorInfo);
      });

      // 捕获 Promise 错误
      window.addEventListener('unhandledrejection', (event) => {
        const errorInfo = {
          message: `Unhandled Promise Rejection: ${event.reason}`,
          reason: event.reason,
          stack: event.reason?.stack,
          timestamp: new Date().toISOString(),
          type: 'promise-rejection'
        };
        window.__pageErrors.push(errorInfo);
        console.error('Captured promise rejection:', errorInfo);
      });

      // 拦截 console.error 和 console.warn
      const originalError = console.error;
      const originalWarn = console.warn;

      console.error = function(...args) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        window.__consoleErrors.push({
          level: 'error',
          message: message,
          timestamp: new Date().toISOString(),
          args: args
        });
        originalError.apply(console, args);
      };

      console.warn = function(...args) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        window.__consoleErrors.push({
          level: 'warn',
          message: message,
          timestamp: new Date().toISOString(),
          args: args
        });
        originalWarn.apply(console, args);
      };

      // Vue 错误处理器设置（延迟执行以确保 Vue 已加载）
      function setupVueErrorHandlers() {
        if (typeof window !== 'undefined') {
          // Vue 3 应用错误处理 - 更全面的方法
          if (window.Vue && window.Vue.createApp) {
            const originalCreateApp = window.Vue.createApp;
            window.Vue.createApp = function(...args) {
              const app = originalCreateApp.apply(this, args);

              app.config.errorHandler = (err, instance, info) => {
                const errorInfo = {
                  message: err.message || err.toString(),
                  stack: err.stack,
                  info: info,
                  componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                  timestamp: new Date().toISOString(),
                  type: 'vue3-error'
                };
                window.__vueErrors.push(errorInfo);
                console.error('Vue 3 Error captured:', errorInfo);
              };

              app.config.warnHandler = (msg, instance, trace) => {
                const warnInfo = {
                  message: msg,
                  trace: trace,
                  componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                  timestamp: new Date().toISOString(),
                  type: 'vue3-warning'
                };
                window.__vueErrors.push(warnInfo);
                console.warn('Vue 3 Warning captured:', warnInfo);
              };

              return app;
            };
          }

          // 尝试直接在已存在的Vue应用上设置错误处理器
          if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__ && window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps) {
            window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.forEach(app => {
              if (app.config) {
                app.config.errorHandler = (err, instance, info) => {
                  const errorInfo = {
                    message: err.message || err.toString(),
                    stack: err.stack,
                    info: info,
                    componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                    timestamp: new Date().toISOString(),
                    type: 'vue3-existing-app-error'
                  };
                  window.__vueErrors.push(errorInfo);
                  console.error('Vue 3 Existing App Error captured:', errorInfo);
                };
              }
            });
          }

          // Vue 2 全局错误处理
          if (window.Vue && window.Vue.config) {
            window.Vue.config.errorHandler = function(err, vm, info) {
              window.__vueErrors.push({
                message: err.message || err.toString(),
                stack: err.stack,
                info: info,
                componentName: vm?.$options?.name || 'Unknown',
                timestamp: new Date().toISOString(),
                type: 'vue2-error'
              });
            };

            window.Vue.config.warnHandler = function(msg, vm, trace) {
              window.__vueErrors.push({
                message: msg,
                trace: trace,
                componentName: vm?.$options?.name || 'Unknown',
                timestamp: new Date().toISOString(),
                type: 'vue2-warning'
              });
            };
          }
        }
      }

      // 立即尝试设置
      setupVueErrorHandlers();

      // 延迟设置以确保 Vue 已加载
      setTimeout(setupVueErrorHandlers, 100);
      setTimeout(setupVueErrorHandlers, 500);
      setTimeout(setupVueErrorHandlers, 1000);
      setTimeout(setupVueErrorHandlers, 2000);

      // 主动检测页面错误的函数
      function detectPageErrors() {
        // 检查是否有错误弹框或错误信息
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '[id*="error"]',
          '[id*="Error"]',
          '.error-message',
          '.error-dialog',
          '.error-modal',
          '.runtime-error',
          '[data-testid*="error"]'
        ].join(','));

        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('错误') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError') ||
            text.includes('ReferenceError') ||
            text.includes('Uncaught')
          )) {
            window.__pageErrors.push({
              message: `DOM Error Element: ${text.substring(0, 200)}`,
              element: element.tagName + (element.className ? '.' + element.className : ''),
              timestamp: new Date().toISOString(),
              type: 'dom-error-element'
            });
          }
        }

        // 检查页面标题是否包含错误信息
        if (document.title && (
          document.title.includes('Error') ||
          document.title.includes('错误') ||
          document.title.includes('404') ||
          document.title.includes('500')
        )) {
          window.__pageErrors.push({
            message: `Page Title Error: ${document.title}`,
            timestamp: new Date().toISOString(),
            type: 'page-title-error'
          });
        }
      }

      // 定期检测页面错误
      setTimeout(detectPageErrors, 1000);
      setTimeout(detectPageErrors, 3000);
      setTimeout(detectPageErrors, 5000);
      setTimeout(detectPageErrors, 8000);

      // 监听DOM变化，检测新出现的错误元素
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const text = node.textContent || node.innerText || '';
                if (text.includes('Uncaught runtime errors') ||
                    text.includes('Cannot read properties of null') ||
                    text.includes('TypeError') ||
                    text.includes('Error')) {
                  window.__pageErrors.push({
                    message: `DOM Mutation Error: ${text.substring(0, 300)}`,
                    timestamp: new Date().toISOString(),
                    type: 'dom-mutation-error'
                  });
                }
              }
            }
          }
        }
      });

      // 确保document.body存在后再观察
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      } else {
        // 如果body还没有加载，等待DOM加载完成
        document.addEventListener('DOMContentLoaded', () => {
          if (document.body) {
            observer.observe(document.body, {
              childList: true,
              subtree: true
            });
          }
        });
      }
    });
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    const totalPages = this.validationResults.length;
    const successfulPages = this.validationResults.filter(r => r.success).length;
    const failedPages = this.validationResults.filter(r => !r.success);

    const report = {
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0
      },
      results: this.validationResults,
      failedPages: failedPages,
      timestamp: new Date().toISOString()
    };

    return report;
  }

  /**
   * 打印验证摘要
   */
  printSummary() {
    const report = this.generateReport();

    console.log(chalk.blue('\n📊 验证结果摘要:'));
    console.log(chalk.gray(`   总页面数: ${report.summary.total}`));
    console.log(chalk.green(`   成功: ${report.summary.successful}`));
    console.log(chalk.red(`   失败: ${report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${report.summary.successRate}%`));

    if (report.failedPages.length > 0) {
      console.log(chalk.red('\n❌ 失败的页面:'));
      for (const failed of report.failedPages) {
        console.log(chalk.red(`   ${failed.route.path}: ${failed.errors[0] || '未知错误'}`));
      }
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }

      if (this.devServer && !this.devServer.killed) {
        this.devServer.kill('SIGTERM');

        // 等待进程结束
        await new Promise((resolve) => {
          this.devServer.on('exit', resolve);
          setTimeout(resolve, 5000); // 5秒超时
        });
      }

      if (this.runtimeErrorHandler) {
        // 清理运行时错误处理器
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  清理资源时出错: ${error.message}`));
      }
    }
  }

  /**
   * 尝试修复页面错误
   */
  async attemptPageErrorFix(route, errors) {
    try {
      if (!this.buildFixAgent) {
        return { success: false, error: 'BuildFixAgent 未初始化' };
      }

      // 构建错误上下文信息
      const errorContext = this.buildPageErrorContext(route, errors);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 分析页面错误: ${route.path}`));
        console.log(chalk.gray(`    错误数量: ${errors.length}`));
      }

      // 分析错误并确定需要修复的文件
      const analysisResult = await this.buildFixAgent.analyzeBuildErrors(errorContext.buildOutput, 1);

      if (!analysisResult.success || !analysisResult.filesToFix || analysisResult.filesToFix.length === 0) {
        return {
          success: false,
          error: '无法确定需要修复的文件',
          analysisResult
        };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    📁 需要修复的文件: ${analysisResult.filesToFix.join(', ')}`));
      }

      // 执行文件修复
      const fixResult = await this.buildFixAgent.fixFiles(
        analysisResult.filesToFix,
        errorContext.buildOutput,
        1
      );

      return {
        success: fixResult.success,
        filesModified: fixResult.filesModified || 0,
        totalFiles: fixResult.totalFiles || 0,
        errors: fixResult.errors,
        analysisResult,
        fixResult
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * 构建页面错误上下文信息
   */
  buildPageErrorContext(route, errors) {
    // 将页面错误转换为类似构建错误的格式，以便 BuildFixAgent 处理
    const errorMessages = errors.map(error => {
      if (typeof error === 'string') {
        return error;
      } else if (error.message) {
        return `${error.type || 'Error'}: ${error.message}`;
      } else {
        return JSON.stringify(error);
      }
    });

    // 使用路由解析器推断可能的组件文件路径
    let suggestedFiles = [];
    if (this.routeParser) {
      const errorMessage = errorMessages.join(' ');
      suggestedFiles = this.routeParser.inferComponentPaths(route.path, errorMessage);
    }

    const buildOutput = `
页面运行时错误报告
==================
页面路径: ${route.path}
页面名称: ${route.name || 'Unknown'}
错误时间: ${new Date().toISOString()}

错误详情:
${errorMessages.map((msg, index) => `${index + 1}. ${msg}`).join('\n')}

可能相关的组件文件:
${suggestedFiles.length > 0 ? suggestedFiles.map(file => `- ${file}`).join('\n') : '- 无法推断具体文件，请根据错误信息分析'}

错误分析:
- 页面访问URL: ${route.path}
- 错误类型: 运行时错误
- 可能原因: Vue组件错误、JavaScript语法错误、依赖问题等
- 建议修复: 检查相关Vue组件和JavaScript文件
- 推荐检查文件: ${suggestedFiles.slice(0, 3).join(', ') || '根据错误信息确定'}

原始错误对象:
${JSON.stringify(errors, null, 2)}
`;

    return {
      buildOutput,
      route,
      errors,
      errorCount: errors.length,
      timestamp: new Date().toISOString(),
      suggestedFiles // 添加推荐的文件列表
    };
  }

  /**
   * 检查页面错误（用于重新验证）
   */
  async checkPageErrors(page) {
    const errors = [];

    try {
      // 检查页面错误数据
      const errorData = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 收集所有错误
      errors.push(...errorData.pageErrors.map(e => ({ type: 'page-error', message: e.message })));
      errors.push(...errorData.vueErrors.map(e => ({ type: 'vue-error', message: e.message })));
      errors.push(...errorData.consoleErrors.filter(e => e.level === 'error').map(e => ({ type: 'console-error', message: e.message })));

      // 检查DOM中的错误元素
      const domErrors = await page.evaluate(() => {
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '.error-message',
          '.runtime-error'
        ].join(','));

        const errors = [];
        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError')
          )) {
            errors.push({
              type: 'dom-error',
              message: text.substring(0, 200)
            });
          }
        }
        return errors;
      });

      errors.push(...domErrors);

    } catch (error) {
      errors.push({
        type: 'check-error',
        message: `检查页面错误时出错: ${error.message}`
      });
    }

    return errors;
  }

  /**
   * 尝试自动登录
   */
  async attemptAutoLogin(page, result) {
    try {
      // 首先访问登录页面检查是否需要登录
      const loginUrl = this.baseUrl + '/#/login';

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔐 检查是否需要登录...`));
      }

      // 访问登录页面
      await page.goto(loginUrl, { waitUntil: 'networkidle0', timeout: 15000 });

      // 等待页面加载和Vue应用初始化
      await this.sleep(3000);

      // 检查是否在登录页面
      const currentUrl = page.url();
      if (currentUrl.includes('/login')) {
        result.needsLogin = true;
        result.loginAttempted = true;

        if (this.options.verbose) {
          console.log(chalk.yellow(`    🔑 检测到需要登录，尝试自动登录...`));
        }

        // 尝试填写登录表单
        const loginSuccess = await this.autoLoginManager.attemptLogin(page);

        if (loginSuccess) {
          if (this.options.verbose) {
            console.log(chalk.green(`    ✅ 自动登录成功`));
          }
          // 登录成功后，设置一个标记，避免后续页面重复登录
          this.isLoggedIn = true;
        } else {
          result.warnings.push('自动登录失败，可能影响页面验证结果');
          if (this.options.verbose) {
            console.log(chalk.yellow(`    ⚠️  自动登录失败`));
          }
        }
      } else {
        // 如果不在登录页面，可能已经登录了
        this.isLoggedIn = true;
      }
    } catch (error) {
      result.warnings.push(`登录检查失败: ${error.message}`);
      if (this.options.verbose) {
        console.log(chalk.yellow(`    ⚠️  登录检查出错: ${error.message}`));
      }
    }
  }

  /**
   * 执行登录操作
   */
  async performLogin(page) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`      🔍 等待登录表单加载...`));
      }

      // 等待登录表单加载
      await page.waitForSelector('.login-form', { timeout: 10000 });

      // 等待Vue应用完全初始化
      await this.sleep(2000);

      // 等待输入框加载并可见
      await page.waitForSelector('input[name="username"]', { visible: true, timeout: 5000 });
      await page.waitForSelector('input[name="password"]', { visible: true, timeout: 5000 });

      if (this.options.verbose) {
        console.log(chalk.gray(`      📝 填写登录信息...`));
      }

      // 使用更稳定的方式清空和填写用户名
      const usernameInput = await page.$('input[name="username"]');
      if (usernameInput) {
        await usernameInput.click({ clickCount: 3 }); // 三击选中所有文本
        await usernameInput.type(this.options.loginCredentials.username, { delay: 100 });
      }

      // 使用更稳定的方式清空和填写密码
      const passwordInput = await page.$('input[name="password"]');
      if (passwordInput) {
        await passwordInput.click({ clickCount: 3 }); // 三击选中所有文本
        await passwordInput.type(this.options.loginCredentials.password, { delay: 100 });
      }

      // 等待一下确保输入完成
      await this.sleep(1000);

      if (this.options.verbose) {
        console.log(chalk.gray(`      🔘 点击登录按钮...`));
      }

      // 查找并点击登录按钮
      const loginButton = await page.$('.el-button--primary');
      if (loginButton) {
        await loginButton.click();

        if (this.options.verbose) {
          console.log(chalk.gray(`      ⏳ 等待登录完成...`));
        }

        // 等待登录请求完成，使用更长的等待时间
        await this.sleep(3000);

        // 等待页面跳转或加载完成
        try {
          await page.waitForFunction(
            () => !window.location.href.includes('/login'),
            { timeout: 10000 }
          );
        } catch (timeoutError) {
          // 如果等待超时，检查当前URL
          if (this.options.verbose) {
            console.log(chalk.yellow(`      ⚠️  等待页面跳转超时，检查当前状态...`));
          }
        }

        // 检查是否登录成功
        const currentUrl = page.url();
        const loginSuccess = !currentUrl.includes('/login');

        if (this.options.verbose) {
          console.log(chalk.gray(`      📍 当前URL: ${currentUrl}`));
          console.log(chalk.gray(`      ${loginSuccess ? '✅' : '❌'} 登录${loginSuccess ? '成功' : '失败'}`));
        }

        return loginSuccess;
      } else {
        if (this.options.verbose) {
          console.log(chalk.yellow(`      ⚠️  未找到登录按钮`));
        }
        return false;
      }

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`      ❌ 登录过程出错: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取验证结果
   */
  getResults() {
    return this.validationResults;
  }

  /**
   * 保存报告到文件
   */
  async saveReport(outputPath) {
    const report = this.generateReport();

    // 生成 Markdown 报告
    const markdown = this.generateMarkdownReport(report);

    await fs.writeFile(outputPath, markdown, 'utf8');
    console.log(chalk.green(`📄 验证报告已保存: ${outputPath}`));
  }

  /**
   * 生成 Markdown 格式的报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 页面验证报告\n\n`;
    markdown += `生成时间: ${report.timestamp}\n\n`;

    markdown += `## 摘要\n\n`;
    markdown += `- 总页面数: ${report.summary.total}\n`;
    markdown += `- 成功: ${report.summary.successful}\n`;
    markdown += `- 失败: ${report.summary.failed}\n`;
    markdown += `- 成功率: ${report.summary.successRate}%\n\n`;

    if (report.failedPages.length > 0) {
      markdown += `## 失败的页面\n\n`;
      for (const failed of report.failedPages) {
        markdown += `### ${failed.route.path}\n\n`;
        markdown += `- URL: ${failed.url}\n`;
        markdown += `- 加载时间: ${failed.loadTime}ms\n`;

        if (failed.errors.length > 0) {
          markdown += `- 错误:\n`;
          for (const error of failed.errors) {
            markdown += `  - ${error}\n`;
          }
        }

        if (failed.warnings.length > 0) {
          markdown += `- 警告:\n`;
          for (const warning of failed.warnings) {
            markdown += `  - ${warning}\n`;
          }
        }

        markdown += `\n`;
      }
    }

    markdown += `## 详细结果\n\n`;
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌';
      markdown += `- ${status} ${result.route.path} (${result.loadTime}ms)\n`;
    }

    return markdown;
  }
}

module.exports = PageValidator;
